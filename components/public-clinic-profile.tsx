"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
	MapPin, 
	Phone, 
	Globe, 
	Clock, 
	Star,
	MessageCircle,
	ExternalLink,
	Stethoscope,
	Calendar,
	Mail
} from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import { Link } from "@/lib/i18n/navigation";
import type { ClinicWithUser } from "@/lib/types/clinic";
import { cn } from "@/lib/utils";

interface PublicClinicProfileProps {
	clinic: ClinicWithUser & {
		wilaya?: { name: string } | null;
		commune?: { name: string } | null;
	};
}

export function PublicClinicProfile({ clinic }: PublicClinicProfileProps) {
	const t = useTranslations("clinics");
	const commonT = useTranslations("common");
	const locale = useLocale();
	
	// Determine text direction based on locale
	const isRTL = locale === "ar";
	const dir = isRTL ? "rtl" : "ltr";

	// Format operating hours for display
	const formatOperatingHours = () => {
		if (!clinic.operatingHours) return null;
		
		const hours = clinic.operatingHours as Record<string, string>;
		const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
		
		return daysOfWeek.map(day => {
			const dayHours = hours[day];
			if (!dayHours) return null;
			
			return {
				day: commonT(`days.${day}`),
				hours: dayHours,
				isToday: new Date().toLocaleDateString('en-US', { weekday: 'lowercase' }) === day
			};
		}).filter(Boolean);
	};

	// Format location display
	const formatLocation = () => {
		const parts = [];
		if (clinic.commune?.name) parts.push(clinic.commune.name);
		if (clinic.wilaya?.name) parts.push(clinic.wilaya.name);
		return parts.join(", ") || clinic.address;
	};

	const operatingHours = formatOperatingHours();

	return (
		<div className="space-y-6" dir={dir}>
			{/* Header Section */}
			<div className="relative">
				<Card className="overflow-hidden">
					<CardContent className="p-8">
						<div className="flex flex-col lg:flex-row gap-6">
							{/* Clinic Info */}
							<div className="flex-1 space-y-4">
								<div className="flex items-start justify-between gap-4">
									<div className="min-w-0 flex-1">
										<div className="flex items-center gap-3 mb-2">
											<h1 className="text-3xl font-bold text-foreground">
												{clinic.name}
											</h1>
											{clinic.featured && (
												<Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
													<Star className="h-4 w-4 mr-1 fill-current" />
													{t("featured")}
												</Badge>
											)}
										</div>
										
										{/* Location */}
										<div className="flex items-center gap-2 text-muted-foreground mb-4">
											<MapPin className="h-5 w-5 shrink-0" />
											<span className="text-lg">{formatLocation()}</span>
										</div>

										{/* Contact Info */}
										<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
											{/* Phone */}
											<div className="flex items-center gap-3">
												<Phone className="h-5 w-5 text-muted-foreground shrink-0" />
												<a 
													href={`tel:${clinic.phone}`}
													className="text-foreground hover:text-primary transition-colors font-medium"
												>
													{clinic.phone}
												</a>
											</div>

											{/* Email */}
											<div className="flex items-center gap-3">
												<Mail className="h-5 w-5 text-muted-foreground shrink-0" />
												<a 
													href={`mailto:${clinic.user.email}`}
													className="text-foreground hover:text-primary transition-colors font-medium"
												>
													{clinic.user.email}
												</a>
											</div>

											{/* Website */}
											{clinic.website && (
												<div className="flex items-center gap-3 sm:col-span-2">
													<Globe className="h-5 w-5 text-muted-foreground shrink-0" />
													<a 
														href={clinic.website}
														target="_blank"
														rel="noopener noreferrer"
														className="text-foreground hover:text-primary transition-colors font-medium flex items-center gap-1"
													>
														{clinic.website.replace(/^https?:\/\//, '')}
														<ExternalLink className="h-4 w-4" />
													</a>
												</div>
											)}
										</div>
									</div>
								</div>

								{/* Action Buttons */}
								<div className="flex flex-wrap gap-3 pt-4">
									<Button asChild size="lg">
										<Link href={`/messages/new?clinic=${clinic.user.slug}`}>
											<MessageCircle className="h-5 w-5 mr-2" />
											{t("contactClinic")}
										</Link>
									</Button>
									
									{clinic.website && (
										<Button asChild variant="outline" size="lg">
											<a 
												href={clinic.website}
												target="_blank"
												rel="noopener noreferrer"
											>
												<Globe className="h-5 w-5 mr-2" />
												{t("visitWebsite")}
											</a>
										</Button>
									)}
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Content Grid */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Main Content */}
				<div className="lg:col-span-2 space-y-6">
					{/* Services */}
					{clinic.services && clinic.services.length > 0 && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Stethoscope className="h-5 w-5" />
									{t("servicesOffered")}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
									{clinic.services.map((service, index) => (
										<div 
											key={index}
											className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg"
										>
											<div className="h-2 w-2 bg-primary rounded-full shrink-0" />
											<span className="font-medium">{service}</span>
										</div>
									))}
								</div>
							</CardContent>
						</Card>
					)}

					{/* Address Details */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<MapPin className="h-5 w-5" />
								{t("location")}
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-2">
								<p className="text-foreground font-medium">{clinic.address}</p>
								<p className="text-muted-foreground">{formatLocation()}</p>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Sidebar */}
				<div className="space-y-6">
					{/* Operating Hours */}
					{operatingHours && operatingHours.length > 0 && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Clock className="h-5 w-5" />
									{t("operatingHours")}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-3">
									{operatingHours.map((item, index) => (
										<div 
											key={index}
											className={cn(
												"flex justify-between items-center py-2",
												item?.isToday && "bg-primary/10 -mx-3 px-3 rounded-md font-medium"
											)}
										>
											<span className={cn(
												"text-sm",
												item?.isToday ? "text-primary font-medium" : "text-muted-foreground"
											)}>
												{item?.day}
											</span>
											<span className={cn(
												"text-sm font-mono",
												item?.isToday ? "text-primary font-medium" : "text-foreground"
											)}>
												{item?.hours}
											</span>
										</div>
									))}
								</div>
							</CardContent>
						</Card>
					)}

					{/* Quick Info */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Calendar className="h-5 w-5" />
								{t("clinicInfo")}
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								<div className="flex justify-between items-center">
									<span className="text-sm text-muted-foreground">{t("memberSince")}</span>
									<span className="text-sm font-medium">
										{new Date(clinic.createdAt).toLocaleDateString(locale, {
											year: 'numeric',
											month: 'long'
										})}
									</span>
								</div>
								
								<Separator />
								
								<div className="flex justify-between items-center">
									<span className="text-sm text-muted-foreground">{t("status")}</span>
									<Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
										{t("verified")}
									</Badge>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
