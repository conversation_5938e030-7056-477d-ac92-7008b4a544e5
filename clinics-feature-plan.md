# Clinic Management System - Implementation Plan

**Version:** 1.1
**Date:** July 26, 2025
**Status:** Phase 1 & 2 Complete, Phase 3 Ready to Begin

---

## Overview

This document outlines the comprehensive implementation plan for the Veterinary Clinic Directory & Listings feature based on the PRD requirements. The system will enable clinics to create profiles, list services, manage cat adoptions, and provide a searchable directory for users.

---

## Current State Analysis

### Existing Infrastructure

- ✅ User authentication system with `clinic` role support
- ✅ Basic clinic profile schema (needs updates per PRD)
- ✅ Cat listing system with clinic role permissions
- ✅ Admin dashboard with basic clinic management
- ✅ i18n support (en, fr, ar) with RTL/LTR handling
- ✅ Mobile-first responsive design framework
- ✅ Next.js 15 App Router with tRPC integration

### Missing Components

- ❌ Clinic onboarding/application system
- ❌ Enhanced clinic profile management
- ❌ Public clinic directory and search
- ❌ Clinic services management
- ❌ Admin approval workflow for clinics
- ❌ Public clinic profile pages
- ❌ SEO optimization for clinic pages

---

## Implementation Phases

## Phase 1: Database Schema & Core Infrastructure (Week 1) ✅ COMPLETED

### 1.1 Database Schema Updates ✅ COMPLETED

**Effort:** 4 hours | **Priority:** Critical | **Dependencies:** None

- [x] Update `clinicProfiles` table schema per PRD requirements
    - Add `services` as `text[]` array
    - Add `operatingHours` as `JSONB`
    - Add `featured` boolean flag
    - Add proper indexes for search performance
- [x] Create database migration script
- [x] Update Drizzle schema definitions
- [x] Add proper relations and constraints

### 1.2 Type Definitions & Validation ✅ COMPLETED

**Effort:** 3 hours | **Priority:** Critical | **Dependencies:** 1.1

- [x] Create TypeScript interfaces for clinic data structures
- [x] Define Zod schemas for clinic profile validation
- [x] Create operating hours type definitions
- [x] Define service tag enums/constants
- [x] Update existing type exports

### 1.3 Core tRPC Router Setup ✅ COMPLETED

**Effort:** 4 hours | **Priority:** Critical | **Dependencies:** 1.1, 1.2

- [x] Implement basic CRUD operations in `clinicsRouter`
- [x] Add clinic profile creation/update procedures
- [x] Add clinic search and filtering procedures
- [x] Add admin-only clinic approval procedures
- [x] Implement proper authorization middleware

## Phase 2: Clinic Profile Management (Week 2) ✅ COMPLETED

### 2.1 Clinic Onboarding System ✅ COMPLETED

**Effort:** 8 hours | **Priority:** High | **Dependencies:** 1.3

- [x] Create clinic application form component
- [x] Implement multi-step onboarding process
- [x] Create application status tracking
- [x] Add email notifications for application updates

### 2.2 Clinic Profile Dashboard ✅ COMPLETED

**Effort:** 10 hours | **Priority:** High | **Dependencies:** 2.1

- [x] Create clinic profile management interface
- [x] Implement operating hours editor with time picker
- [x] Build services management system with tag selection
- [x] Add profile completion progress indicator
- [x] Integrate with existing profile layout system

### 2.3 Services Management ✅ COMPLETED → 🔄 UPDATED TO TWO-TIER SYSTEM

**Effort:** 6 hours | **Priority:** Medium | **Dependencies:** 2.2

- [x] Replace mock `ClinicServices` component with real data
- [x] Implement service CRUD operations
- [x] Add service availability toggle
- [x] Create service pricing management
- [x] Add service category organization
- [x] Add comprehensive i18n support for service management

**NEW: Two-Tier Service Management System Requirements** ✅ COMPLETED

**Effort:** 8 hours | **Priority:** High | **Dependencies:** Admin system

- [x] **Admin Service Type Management**

    - [x] Create global service types that only admins can manage
    - [x] Implement admin interface for service type CRUD operations
    - [x] Add service type categories and standardized descriptions
    - [x] Create service type approval workflow for new requests
    - [x] Add comprehensive search and filtering capabilities
    - [x] Implement service type activation/deactivation controls

- [x] **Clinic Service Selection System**
    - [x] Replace free-form service creation with selection from admin-created types
    - [x] Allow clinics to customize pricing and availability for selected services
    - [x] Add clinic-specific service descriptions while maintaining type consistency
    - [x] Implement proper role-based access control
    - [x] Create seamless service selection interface with search and filtering

**Implementation Status:**

- ✅ Database migration completed successfully (8 service types created, 4 clinic services updated)
- ✅ Admin service management interface fully functional at `/admin/service-types`
- ✅ Clinic service selection interface implemented at `/profile/services/add`
- ✅ All tRPC procedures updated with proper authorization
- ✅ Complete internationalization support (EN/AR/FR)
- ✅ Role-based navigation and access control implemented

## Phase 3: Admin Management System (Week 1-2) → 🔄 UPDATED WITH SERVICE MANAGEMENT

### 3.1 Admin Clinic Management

**Effort:** 6 hours | **Priority:** High | **Dependencies:** 1.3

- [ ] Replace placeholder `AdminClinicsList` component
- [ ] Implement clinic approval/rejection workflow
- [ ] Add clinic profile editing capabilities for admins
- [ ] Add featured clinic toggle functionality
- [ ] Implement bulk operations for clinic management

### 3.2 Admin Service Type Management ✅ COMPLETED

**Effort:** 8 hours | **Priority:** High | **Dependencies:** 3.1

- [x] **Database Schema Updates**

    - [x] Create `service_types` table for admin-managed service definitions
    - [x] Add foreign key relationship from `clinic_services` to `service_types`
    - [x] Create migration script to convert existing services to new structure
    - [x] Add indexes for performance optimization

- [x] **Admin Service Type Interface**

    - [x] Create admin service type management page (`/admin/service-types`)
    - [x] Implement service type CRUD operations with proper validation
    - [x] Add service type categories and standardized descriptions
    - [x] Create comprehensive search and filtering functionality
    - [x] Add service type activation/deactivation controls

- [x] **Service Type tRPC Procedures**
    - [x] Add admin-only service type creation/update/delete procedures
    - [x] Implement service type listing and search functionality
    - [x] Add service type toggle active/inactive functionality
    - [x] Create proper authorization middleware for admin-only access
    - [x] Implement comprehensive error handling and validation

### 3.3 Admin Dashboard Integration 🔄 PARTIALLY COMPLETED

**Effort:** 3 hours | **Priority:** High | **Dependencies:** 3.1, 3.2

- [ ] Update admin dashboard stats to include clinic metrics
- [ ] Add clinic-specific analytics and reporting
- [ ] Integrate clinic management into existing admin tabs
- [ ] Add clinic application notifications/alerts
- [x] **NEW:** Add service type management navigation to admin menu
- [x] **NEW:** Implement role-based navigation for admin users
- [x] **NEW:** Add service type management access controls

## Phase 4: Public Clinic Directory (Week 3)

### 4.1 Clinic Directory Page

**Effort:** 12 hours | **Priority:** High | **Dependencies:** 1.3

- [ ] Create public clinic directory page (`/clinics`)
- [ ] Implement advanced search and filtering
    - Location-based filtering (wilaya/commune)
    - Service-based filtering
    - Name/keyword search
    - Featured clinics prioritization
- [ ] Add map integration for clinic locations
- [ ] Implement pagination and infinite scroll
- [ ] Add sorting options (distance, rating, featured)

### 4.2 Individual Clinic Profile Pages

**Effort:** 10 hours | **Priority:** High | **Dependencies:** 4.1

- [ ] Create public clinic profile pages (`/clinics/[slug]`)
- [ ] Display comprehensive clinic information
- [ ] Show services offered with availability status
- [ ] Display operating hours in user-friendly format
- [ ] Add contact information and directions
- [ ] Integrate with cat listings from the clinic

### 4.3 SEO & Performance Optimization

**Effort:** 6 hours | **Priority:** Medium | **Dependencies:** 4.2

- [ ] Implement proper meta tags and structured data
- [ ] Add Open Graph and Twitter Card support
- [ ] Optimize images and loading performance
- [ ] Implement proper URL structure and sitemap
- [ ] Add breadcrumb navigation

## Phase 5: Integration & User Experience (Week 3-4)

### 5.1 Navigation Integration

**Effort:** 4 hours | **Priority:** Medium | **Dependencies:** 4.1

- [ ] Add clinic directory to main navigation
- [ ] Update navbar configuration
- [ ] Add clinic-specific user menu items
- [ ] Implement proper routing and redirects

### 5.2 Cat Listing Integration

**Effort:** 6 hours | **Priority:** Medium | **Dependencies:** 4.2

- [ ] Add clinic information to cat detail pages
- [ ] Create clinic-specific cat listing views
- [ ] Implement cross-linking between clinics and cats
- [ ] Add clinic verification badges

### 5.3 Messaging System Integration

**Effort:** 4 hours | **Priority:** Low | **Dependencies:** 5.2

- [ ] Enable messaging between users and clinics
- [ ] Add clinic-specific message templates
- [ ] Implement appointment request functionality
- [ ] Add service inquiry messaging

## Phase 6: Internationalization & Mobile Optimization (Week 4)

### 6.1 i18n Implementation

**Effort:** 8 hours | **Priority:** High | **Dependencies:** All UI components

- [ ] Add clinic-related translations to all language files
    - English translations
    - French translations
    - Arabic translations with RTL support
- [ ] Implement proper text direction handling
- [ ] Add locale-specific formatting for hours/dates
- [ ] Test all clinic features in all supported languages

### 6.2 Mobile-First Responsive Design

**Effort:** 6 hours | **Priority:** High | **Dependencies:** 6.1

- [ ] Optimize clinic directory for mobile devices
- [ ] Ensure 44px minimum touch targets
- [ ] Test at required breakpoints (320px, 768px, 1024px, 1440px)
- [ ] Implement mobile-specific navigation patterns
- [ ] Optimize forms for mobile input

### 6.3 Accessibility & Testing

**Effort:** 4 hours | **Priority:** Medium | **Dependencies:** 6.2

- [ ] Implement ARIA labels and semantic HTML
- [ ] Add keyboard navigation support
- [ ] Test with screen readers
- [ ] Ensure color contrast compliance
- [ ] Add focus management for modals/forms

---

## Technical Specifications

### Database Schema Changes ✅ COMPLETED

```sql
-- Updated clinic_profiles table with wilaya/commune integration
CREATE TABLE clinic_profiles (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) UNIQUE,
  name TEXT NOT NULL,
  address TEXT NOT NULL,
  wilaya_id INTEGER REFERENCES wilayas(id),
  commune_id INTEGER REFERENCES communes(id),
  phone TEXT NOT NULL,
  website TEXT,
  services TEXT[],
  operating_hours JSONB,
  featured BOOLEAN DEFAULT false NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_clinic_profiles_services ON clinic_profiles USING GIN(services);
CREATE INDEX idx_clinic_profiles_featured ON clinic_profiles(featured);
CREATE INDEX idx_clinic_profiles_wilaya ON clinic_profiles(wilaya_id);
CREATE INDEX idx_clinic_profiles_commune ON clinic_profiles(commune_id);
CREATE INDEX idx_clinic_profiles_search ON clinic_profiles(name, address);
```

### API Endpoints Structure

```typescript
// tRPC Router Structure
clinicsRouter = {
	// Public procedures
	getAll: publicProcedure, // Get all approved clinics
	getBySlug: publicProcedure, // Get clinic by slug
	search: publicProcedure, // Search clinics with filters

	// Protected procedures (clinic role)
	getProfile: protectedProcedure, // Get own clinic profile
	updateProfile: protectedProcedure, // Update own profile
	applyForRole: protectedProcedure, // Apply for clinic role

	// Admin procedures
	approve: adminProcedure, // Approve clinic application
	reject: adminProcedure, // Reject clinic application
	setFeatured: adminProcedure, // Toggle featured status
	adminList: adminProcedure, // List all clinics for admin
};
```

### URL Structure ✅ UPDATED → ✅ SERVICE MANAGEMENT COMPLETED

```
/clinics                    # Public clinic directory (planned)
/clinics/[slug]            # Individual clinic profile (planned)
/clinic                    # Clinic profile management (authenticated) ✅ IMPLEMENTED
/profile/services          # Clinic service management ✅ IMPLEMENTED
/profile/services/add      # Service selection form ✅ IMPLEMENTED
/admin/clinics            # Admin clinic management (planned)
/admin/service-types      # Admin service type management ✅ IMPLEMENTED
```

**Recent Changes:**

- ✅ Moved clinic profile from `/profile/clinic/profile` to `/profile/clinic` for better UX
- ✅ Updated navigation and routing to support new structure
- ✅ Integrated with existing profile layout system
- ✅ **COMPLETED:** Updated service management to two-tier system (admin-created types + clinic selection)
- ✅ **COMPLETED:** Added admin service type management routes and interfaces
- ✅ **COMPLETED:** Implemented database migration for service types system
- ✅ **COMPLETED:** Added comprehensive role-based access control
- ✅ **COMPLETED:** Full internationalization support for service management

---

## Dependencies & Integration Points

### External Dependencies

- **Maps Integration:** Consider Google Maps or OpenStreetMap for location display
- **Image Storage:** Existing image upload system for clinic photos
- **Email Service:** Existing Resend integration for notifications

### Internal Dependencies

- **Authentication System:** Better Auth integration
- **User Management:** Existing user role system
- **Location System:** Existing wilaya/commune structure
- **Admin System:** Existing admin dashboard framework
- **i18n System:** Next-intl configuration

---

## Risk Assessment & Mitigation

### High Risk Items

1. **Database Migration Complexity**

    - _Risk:_ Schema changes affecting existing data
    - _Mitigation:_ Thorough testing in staging environment

2. **Performance Impact**

    - _Risk:_ New search queries affecting database performance
    - _Mitigation:_ Proper indexing and query optimization

3. **User Experience Consistency**
    - _Risk:_ Clinic features not matching existing design patterns
    - _Mitigation:_ Follow established component library and design system

### Medium Risk Items

1. **i18n Complexity:** RTL support for Arabic
2. **Mobile Performance:** Large clinic directory pages
3. **SEO Implementation:** Proper structured data and meta tags

---

## Success Metrics

### Technical Metrics

- [ ] All clinic pages load in <2 seconds
- [ ] Mobile Lighthouse score >90
- [ ] Zero accessibility violations
- [ ] 100% i18n coverage for clinic features

### Business Metrics

- [ ] Clinic application completion rate >80%
- [ ] Admin approval workflow efficiency
- [ ] User engagement with clinic directory
- [ ] Cross-referencing between clinics and cat adoptions

---

## Recent Progress Summary (July 26, 2025)

### ✅ Completed Work

**Phase 1: Database Schema & Core Infrastructure** - FULLY COMPLETED

- ✅ Updated database schema with wilaya/commune integration
- ✅ Migrated from city/state text fields to normalized location references
- ✅ Created comprehensive TypeScript types and Zod validation schemas
- ✅ Implemented complete tRPC router with CRUD operations
- ✅ Added proper authorization middleware and error handling

**Phase 2: Clinic Profile Management** - FULLY COMPLETED

- ✅ Built comprehensive clinic profile form with wilaya/commune dropdowns
- ✅ Implemented operating hours editor with structured time management
- ✅ Created services selector component with tag-based selection
- ✅ Integrated with existing profile layout and navigation system
- ✅ Added proper i18n support for all clinic-related text
- ✅ Implemented complete services management system with real data integration
- ✅ Added service CRUD operations with proper form validation
- ✅ Created service availability toggles and pricing management
- ✅ Added comprehensive i18n support for service management features

**Route Restructuring & Navigation** - COMPLETED

- ✅ Moved clinic profile from `/profile/clinic/profile` to `/clinic`
- ✅ Updated sidebar navigation with "Clinic Settings" item
- ✅ Fixed active route detection and navigation flow
- ✅ Cleaned up old route files and updated all references

**Database Migration & Schema Updates** - COMPLETED

- ✅ Created comprehensive migration script for location data
- ✅ Updated all TypeScript interfaces and validation schemas
- ✅ Fixed tRPC router to use new wilayaId/communeId structure
- ✅ Added proper relations and indexes for performance

### 🔄 Current Status

**Phase 2: Clinic Profile Management** - FULLY COMPLETED ✅

All clinic profile management features have been successfully implemented with real data integration and comprehensive i18n support.

**Next Phase: Phase 3 - Admin Management System** - READY TO BEGIN

### 📋 Next Steps → ✅ SERVICE MANAGEMENT COMPLETED

1. **Immediate Actions (Next)**

    - ✅ **COMPLETED:** Implement two-tier service management system
    - ✅ **COMPLETED:** Create admin service type management interface and database schema
    - ✅ **COMPLETED:** Update clinic service selection to use admin-created service types
    - ✅ **COMPLETED:** Implement proper role-based access control for service management
    - **NEXT:** Begin Phase 3.1: Admin clinic management implementation
    - **NEXT:** Replace placeholder AdminClinicsList component with real functionality

2. **Short-term Goals (Next 1-2 weeks)**

    - ✅ **COMPLETED:** Complete admin service type management system (Phase 3.2)
    - ✅ **COMPLETED:** Migrate existing clinic services to new two-tier structure
    - **IN PROGRESS:** Complete admin management system (Phase 3)
    - **NEXT:** Begin public clinic directory implementation (Phase 4)
    - **ONGOING:** Test all implemented features thoroughly

3. **Medium-term Goals (Next 2-4 weeks)**
    - **NEXT:** Complete public-facing clinic directory and profile pages
    - **NEXT:** Implement SEO optimization and performance enhancements
    - **NEXT:** Finalize i18n coverage and mobile optimization
    - **FUTURE:** Add service type request system for clinics to suggest new types

---

_This plan is actively maintained and updated as development progresses._
