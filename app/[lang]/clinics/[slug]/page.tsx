import { notFound } from "next/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { PublicClinicProfile } from "@/components/public-clinic-profile";

interface ClinicProfilePageProps {
	params: Promise<{
		slug: string;
		lang: Locale;
	}>;
}

async function getClinic(slug: string) {
	try {
		return await api.clinics.getBySlug(slug);
	} catch (error) {
		return null;
	}
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	const { slug, lang } = await params;
	const clinic = await getClinic(slug);
	const common = await getTranslations({ locale: lang, namespace: "common" });
	const t = await getTranslations({ locale: lang, namespace: "clinics" });

	if (!clinic) {
		return {
			title: `${t("notFound")} - ${common("appName")}`,
			description: t("notFoundDescription"),
		};
	}

	// Format location for description
	const locationParts = [];
	if (clinic.commune?.name) locationParts.push(clinic.commune.name);
	if (clinic.wilaya?.name) locationParts.push(clinic.wilaya.name);
	const location = locationParts.join(", ");

	const description = `${clinic.name} - ${t("profileDescription", { location })}`;

	return {
		title: `${clinic.name} - ${t("veterinaryClinic")} | ${common("appName")}`,
		description,
		openGraph: {
			title: `${clinic.name} - ${t("veterinaryClinic")}`,
			description,
			type: "website",
			url: `/clinics/${slug}`,
		},
		twitter: {
			card: "summary_large_image",
			title: `${clinic.name} - ${t("veterinaryClinic")}`,
			description,
		},
		alternates: {
			canonical: `/clinics/${slug}`,
		},
	};
}

export default async function ClinicProfilePage({
	params,
}: ClinicProfilePageProps) {
	const { slug, lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	const clinic = await getClinic(slug);

	if (!clinic) {
		notFound();
	}

	// Only show approved clinics to the public
	if (clinic.status !== "approved") {
		notFound();
	}

	return (
		<>
			<main className="container mx-auto px-4 py-8 max-w-6xl">
				<PublicClinicProfile clinic={clinic} />
			</main>
		</>
	);
}
