import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { eq, desc, asc, like, count, sql, and, or, inArray } from "drizzle-orm";
import {
	createTRPCRouter as router,
	publicProcedure,
	protectedProcedure,
	protectedAdminProcedure,
} from "../trpc";
import {
	clinicProfiles,
	users,
	clinicServices,
	serviceTypes,
} from "@/lib/db/schema";
import {
	clinicProfileUpdateSchema,
	clinicSearchSchema,
	clinicServiceCreateSchema,
	clinicServiceUpdateSchema,
	clinicServiceFilterSchema,
	type ClinicProfile,
	type ClinicWithUser,
	type ClinicService,
	type ClinicServiceWithType,
} from "@/lib/types/clinic";
import { logSlowQuery } from "./helpers/cat-helpers";

export const clinicsRouter = router({
	// Public procedures

	// Get all approved clinics with search and filtering
	getAll: publicProcedure
		.input(clinicSearchSchema)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const {
				query,
				wilayaId,
				communeId,
				services,
				featured,
				limit,
				offset,
			} = input;

			// Build where conditions
			const conditions = [];

			if (query) {
				conditions.push(
					or(
						like(clinicProfiles.name, `%${query}%`),
						like(clinicProfiles.address, `%${query}%`)
					)
				);
			}

			if (wilayaId) {
				conditions.push(eq(clinicProfiles.wilayaId, wilayaId));
			}

			if (communeId) {
				conditions.push(eq(clinicProfiles.communeId, communeId));
			}

			if (services && services.length > 0) {
				// Use array overlap operator for services filtering
				conditions.push(sql`${clinicProfiles.services} && ${services}`);
			}

			if (featured !== undefined) {
				conditions.push(eq(clinicProfiles.featured, featured));
			}

			const whereClause =
				conditions.length > 0 ? and(...conditions) : undefined;

			// Get clinics with user information
			const clinicsData = await ctx.db.query.clinicProfiles.findMany({
				where: whereClause,
				with: {
					user: {
						columns: {
							id: true,
							name: true,
							email: true,
							slug: true,
							image: true,
							emailVerified: true,
						},
					},
					wilaya: true,
					commune: true,
				},
				limit,
				offset,
				orderBy: [
					desc(clinicProfiles.featured),
					desc(clinicProfiles.createdAt),
				],
			});

			// Get total count for pagination
			const [totalResult] = await ctx.db
				.select({ count: count() })
				.from(clinicProfiles)
				.where(whereClause);

			const duration = performance.now() - startTime;
			logSlowQuery("getAllClinics", duration);

			return {
				clinics: clinicsData as ClinicWithUser[],
				total: totalResult.count,
				hasMore: offset + limit < totalResult.count,
			};
		}),

	// Get clinic by slug (public profile)
	getBySlug: publicProcedure
		.input(z.string())
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();

			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(users.slug, input),
				with: {
					user: {
						columns: {
							id: true,
							name: true,
							email: true,
							slug: true,
							image: true,
							emailVerified: true,
						},
					},
					wilaya: true,
					commune: true,
				},
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			const duration = performance.now() - startTime;
			logSlowQuery("getClinicBySlug", duration);

			return clinic as ClinicWithUser;
		}),

	// Protected procedures (clinic role)

	// Get own clinic profile
	getProfile: protectedProcedure.query(async ({ ctx }) => {
		const userId = Number(ctx.user.id);

		if (isNaN(userId)) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: "Invalid user ID",
			});
		}

		const clinic = await ctx.db.query.clinicProfiles.findFirst({
			where: eq(clinicProfiles.userId, userId),
		});

		if (!clinic) {
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "Clinic profile not found",
			});
		}

		return clinic as ClinicProfile;
	}),

	// Create or update clinic profile
	updateProfile: protectedProcedure
		.input(clinicProfileUpdateSchema)
		.mutation(async ({ ctx, input }) => {
			const userId = Number(ctx.user.id);

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Check if user has clinic role
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.id, userId),
			});

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message:
						"Only users with clinic role can manage clinic profiles",
				});
			}

			// Check if clinic profile exists
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, userId),
			});

			const updateData = {
				...input,
				updatedAt: new Date(),
			};

			if (existingClinic) {
				// Update existing profile
				const [updatedClinic] = await ctx.db
					.update(clinicProfiles)
					.set(updateData)
					.where(eq(clinicProfiles.userId, userId))
					.returning();

				return updatedClinic as ClinicProfile;
			} else {
				// Create new profile - ensure required fields are provided
				if (!input.name || !input.address || !input.phone) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message:
							"Name, address, and phone are required for creating a clinic profile",
					});
				}

				const [newClinic] = await ctx.db
					.insert(clinicProfiles)
					.values({
						userId,
						name: input.name,
						address: input.address,
						phone: input.phone,
						wilayaId: input.wilayaId || null,
						communeId: input.communeId || null,
						website: input.website || null,
						services: input.services || null,
						operatingHours: input.operatingHours || null,
						updatedAt: new Date(),
					})
					.returning();

				return newClinic as ClinicProfile;
			}
		}),

	// Admin procedures

	// Get all clinics for admin management
	adminList: protectedAdminProcedure
		.input(
			z.object({
				limit: z.number().min(1).max(100).default(20),
				offset: z.number().min(0).default(0),
				search: z.string().optional(),
				featured: z.boolean().optional(),
				status: z.enum(["pending", "approved", "rejected"]).optional(),
				sortBy: z
					.enum([
						"name",
						"address",
						"createdAt",
						"featured",
						"status",
					])
					.default("createdAt"),
				sortOrder: z.enum(["asc", "desc"]).default("desc"),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const {
				limit,
				offset,
				search,
				featured,
				status,
				sortBy,
				sortOrder,
			} = input;

			// Build where conditions
			const conditions = [];

			if (search) {
				conditions.push(
					or(
						like(clinicProfiles.name, `%${search}%`),
						like(clinicProfiles.address, `%${search}%`)
					)
				);
			}

			if (featured !== undefined) {
				conditions.push(eq(clinicProfiles.featured, featured));
			}

			if (status !== undefined) {
				conditions.push(eq(clinicProfiles.status, status));
			}

			const whereClause =
				conditions.length > 0 ? and(...conditions) : undefined;

			// Determine sort order
			const orderBy = sortOrder === "asc" ? asc : desc;
			let sortColumn;
			switch (sortBy) {
				case "name":
					sortColumn = clinicProfiles.name;
					break;
				case "address":
					sortColumn = clinicProfiles.address;
					break;
				case "featured":
					sortColumn = clinicProfiles.featured;
					break;
				case "status":
					sortColumn = clinicProfiles.status;
					break;
				default:
					sortColumn = clinicProfiles.createdAt;
			}

			// Get clinics with user information
			const clinicsData = await ctx.db.query.clinicProfiles.findMany({
				where: whereClause,
				with: {
					user: {
						columns: {
							id: true,
							name: true,
							email: true,
							slug: true,
							image: true,
							emailVerified: true,
						},
					},
					wilaya: true,
					commune: true,
				},
				limit,
				offset,
				orderBy: [orderBy(sortColumn)],
			});

			// Get total count
			const [totalResult] = await ctx.db
				.select({ count: count() })
				.from(clinicProfiles)
				.where(whereClause);

			const duration = performance.now() - startTime;
			logSlowQuery("adminListClinics", duration);

			return {
				clinics: clinicsData as ClinicWithUser[],
				total: totalResult.count,
			};
		}),

	// Toggle featured status (admin only)
	setFeatured: protectedAdminProcedure
		.input(
			z.object({
				clinicId: z.number(),
				featured: z.boolean(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const { clinicId, featured } = input;

			const [updatedClinic] = await ctx.db
				.update(clinicProfiles)
				.set({ featured, updatedAt: new Date() })
				.where(eq(clinicProfiles.id, clinicId))
				.returning();

			if (!updatedClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			return updatedClinic as ClinicProfile;
		}),

	// Delete clinic profile (admin only)
	delete: protectedAdminProcedure
		.input(z.object({ clinicId: z.number() }))
		.mutation(async ({ ctx, input }) => {
			const { clinicId } = input;

			const [deletedClinic] = await ctx.db
				.delete(clinicProfiles)
				.where(eq(clinicProfiles.id, clinicId))
				.returning();

			if (!deletedClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			return { success: true, message: "Clinic deleted successfully" };
		}),

	// Update clinic status (admin only)
	updateStatus: protectedAdminProcedure
		.input(
			z.object({
				clinicId: z.number(),
				status: z.enum(["pending", "approved", "rejected"]),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const { clinicId, status } = input;

			const [updatedClinic] = await ctx.db
				.update(clinicProfiles)
				.set({ status, updatedAt: new Date() })
				.where(eq(clinicProfiles.id, clinicId))
				.returning();

			if (!updatedClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			return updatedClinic as ClinicProfile;
		}),

	// Bulk approve clinics (admin only)
	bulkApprove: protectedAdminProcedure
		.input(z.object({ clinicIds: z.array(z.number()) }))
		.mutation(async ({ ctx, input }) => {
			const { clinicIds } = input;

			const updatedClinics = await ctx.db
				.update(clinicProfiles)
				.set({ status: "approved", updatedAt: new Date() })
				.where(inArray(clinicProfiles.id, clinicIds))
				.returning();

			return {
				success: true,
				message: `${updatedClinics.length} clinics approved successfully`,
				updatedCount: updatedClinics.length,
			};
		}),

	// Bulk reject clinics (admin only)
	bulkReject: protectedAdminProcedure
		.input(z.object({ clinicIds: z.array(z.number()) }))
		.mutation(async ({ ctx, input }) => {
			const { clinicIds } = input;

			const updatedClinics = await ctx.db
				.update(clinicProfiles)
				.set({ status: "rejected", updatedAt: new Date() })
				.where(inArray(clinicProfiles.id, clinicIds))
				.returning();

			return {
				success: true,
				message: `${updatedClinics.length} clinics rejected successfully`,
				updatedCount: updatedClinics.length,
			};
		}),

	// Admin edit clinic profile (admin only)
	adminUpdate: protectedAdminProcedure
		.input(
			z.object({
				clinicId: z.number(),
				data: clinicProfileUpdateSchema.partial(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const { clinicId, data } = input;

			const [updatedClinic] = await ctx.db
				.update(clinicProfiles)
				.set({ ...data, updatedAt: new Date() })
				.where(eq(clinicProfiles.id, clinicId))
				.returning();

			if (!updatedClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			return updatedClinic as ClinicProfile;
		}),

	// Get clinic details for admin editing (admin only)
	adminGetById: protectedAdminProcedure
		.input(z.object({ clinicId: z.number() }))
		.query(async ({ ctx, input }) => {
			const { clinicId } = input;

			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, clinicId),
				with: {
					user: {
						columns: {
							id: true,
							name: true,
							email: true,
							slug: true,
						},
					},
				},
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			return clinic as ClinicWithUser;
		}),

	// Service Management Procedures

	// Get services for a clinic (public) - updated for two-tier system
	getServices: publicProcedure
		.input(z.object({ clinicId: z.number() }))
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { clinicId } = input;

			const services = await ctx.db.query.clinicServices.findMany({
				where: eq(clinicServices.clinicId, clinicId),
				with: {
					serviceType: true,
				},
				orderBy: [asc(clinicServices.displayOrder)],
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getClinicServices", duration);

			return services as ClinicServiceWithType[];
		}),

	// Get own clinic services (protected) - updated for two-tier system
	getOwnServices: protectedProcedure.query(async ({ ctx }) => {
		const userId = Number(ctx.user.id);

		if (isNaN(userId)) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: "Invalid user ID",
			});
		}

		// First get the clinic profile
		const clinic = await ctx.db.query.clinicProfiles.findFirst({
			where: eq(clinicProfiles.userId, userId),
		});

		if (!clinic) {
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "Clinic profile not found",
			});
		}

		// Get services for this clinic with service type information
		const services = await ctx.db.query.clinicServices.findMany({
			where: eq(clinicServices.clinicId, clinic.id),
			with: {
				serviceType: true,
			},
			orderBy: [asc(clinicServices.displayOrder)],
		});

		return services as ClinicServiceWithType[];
	}),

	// Add service to clinic (protected - clinic role) - updated for two-tier system
	addService: protectedProcedure
		.input(clinicServiceCreateSchema)
		.mutation(async ({ ctx, input }) => {
			const userId = Number(ctx.user.id);

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Verify user has clinic role
			if (ctx.user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can add services",
				});
			}

			// Get the clinic profile
			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, userId),
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic profile not found",
				});
			}

			// Verify the service type exists and is active
			const serviceType = await ctx.db.query.serviceTypes.findFirst({
				where: eq(serviceTypes.id, input.serviceTypeId),
			});

			if (!serviceType) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service type not found",
				});
			}

			if (!serviceType.isActive) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Service type is not active",
				});
			}

			// Check if clinic already has this service type
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: and(
						eq(clinicServices.clinicId, clinic.id),
						eq(clinicServices.serviceTypeId, input.serviceTypeId)
					),
				}
			);

			if (existingService) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "Clinic already offers this service type",
				});
			}

			// Add the service to clinic
			const [newService] = await ctx.db
				.insert(clinicServices)
				.values({
					clinicId: clinic.id,
					serviceTypeId: input.serviceTypeId,
					price: input.price || null,
					isAvailable: input.isAvailable,
					customDescription: input.customDescription || null,
					displayOrder: input.displayOrder,
					updatedAt: new Date(),
				})
				.returning();

			// Return service with type information
			const serviceWithType = await ctx.db.query.clinicServices.findFirst(
				{
					where: eq(clinicServices.id, newService.id),
					with: {
						serviceType: true,
					},
				}
			);

			return serviceWithType as ClinicServiceWithType;
		}),

	// Update a service (protected - clinic role) - updated for two-tier system
	updateService: protectedProcedure
		.input(
			z.object({
				serviceId: z.number(),
				data: clinicServiceUpdateSchema,
			})
		)
		.mutation(async ({ ctx, input }) => {
			const userId = Number(ctx.user.id);
			const { serviceId, data } = input;

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Verify user has clinic role
			if (ctx.user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can update services",
				});
			}

			// Get the clinic profile
			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, userId),
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic profile not found",
				});
			}

			// Verify the service belongs to this clinic
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: and(
						eq(clinicServices.id, serviceId),
						eq(clinicServices.clinicId, clinic.id)
					),
				}
			);

			if (!existingService) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message:
						"Service not found or does not belong to your clinic",
				});
			}

			// Update the service
			const [updatedService] = await ctx.db
				.update(clinicServices)
				.set({
					...data,
					updatedAt: new Date(),
				})
				.where(eq(clinicServices.id, serviceId))
				.returning();

			// Return service with type information
			const serviceWithType = await ctx.db.query.clinicServices.findFirst(
				{
					where: eq(clinicServices.id, updatedService.id),
					with: {
						serviceType: true,
					},
				}
			);

			return serviceWithType as ClinicServiceWithType;
		}),

	// Delete a service (protected - clinic role)
	deleteService: protectedProcedure
		.input(z.object({ serviceId: z.number() }))
		.mutation(async ({ ctx, input }) => {
			const userId = Number(ctx.user.id);
			const { serviceId } = input;

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Verify user has clinic role
			if (ctx.user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can delete services",
				});
			}

			// Get the clinic profile
			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, userId),
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic profile not found",
				});
			}

			// Verify the service belongs to this clinic
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: and(
						eq(clinicServices.id, serviceId),
						eq(clinicServices.clinicId, clinic.id)
					),
				}
			);

			if (!existingService) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message:
						"Service not found or does not belong to your clinic",
				});
			}

			// Delete the service
			await ctx.db
				.delete(clinicServices)
				.where(eq(clinicServices.id, serviceId));

			return { success: true, message: "Service deleted successfully" };
		}),

	// Toggle service availability (protected - clinic role)
	toggleServiceAvailability: protectedProcedure
		.input(z.object({ serviceId: z.number() }))
		.mutation(async ({ ctx, input }) => {
			const userId = Number(ctx.user.id);
			const { serviceId } = input;

			if (isNaN(userId)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invalid user ID",
				});
			}

			// Verify user has clinic role
			if (ctx.user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message:
						"Only clinic users can toggle service availability",
				});
			}

			// Get the clinic profile
			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, userId),
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic profile not found",
				});
			}

			// Get the service and verify it belongs to this clinic
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: and(
						eq(clinicServices.id, serviceId),
						eq(clinicServices.clinicId, clinic.id)
					),
				}
			);

			if (!existingService) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message:
						"Service not found or does not belong to your clinic",
				});
			}

			// Toggle availability
			const [updatedService] = await ctx.db
				.update(clinicServices)
				.set({
					isAvailable: !existingService.isAvailable,
					updatedAt: new Date(),
				})
				.where(eq(clinicServices.id, serviceId))
				.returning();

			return updatedService as ClinicService;
		}),
});
